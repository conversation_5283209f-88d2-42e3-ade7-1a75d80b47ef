if (!customElements.get('variant-secondary-image')) {
  customElements.define(
    'variant-secondary-image',
    class VariantSecondaryImage extends HTMLElement {
      constructor() {
        super();
        console.log('=== VARIANT SECONDARY IMAGE COMPONENT INITIALIZED ===');

        this.mediaGallery = this.closest('product-info').querySelector('media-gallery');
        this.productModal = this.closest('product-info').querySelector('product-modal');

        console.log('Media gallery found:', this.mediaGallery);
        console.log('Product modal found:', this.productModal);

        this.currentVariantId = null;
        this.secondaryImageElement = null;
        this.secondaryThumbnailElement = null;
        this.secondaryModalElement = null;
        this.variantMetafields = this.getVariantMetafields();
        this.preloadedImages = new Map();

        console.log('Variant metafields loaded:', this.variantMetafields);

        // Start preloading images
        this.preloadSecondaryImages();
      }

      getVariantMetafields() {
        const variantSelects = this.closest('product-info').querySelector('variant-selects');
        if (variantSelects) {
          const metafieldsScript = variantSelects.querySelector('[data-variant-metafields]');
          if (metafieldsScript) {
            try {
              return JSON.parse(metafieldsScript.innerHTML);
            } catch (e) {
              console.error('Error parsing variant metafields:', e);
            }
          }
        }
        return {};
      }

      preloadSecondaryImages() {
        console.log('Starting to preload secondary images...');

        // Use requestIdleCallback for better performance, fallback to setTimeout
        const preloadFunction = () => {
          const variantsWithImages = Object.entries(this.variantMetafields).filter(
            ([variantId, metafields]) => metafields?.secondary_image
          );

          if (variantsWithImages.length === 0) {
            console.log('No variants with secondary images found');
            return;
          }

          // Strategy: Only preload a limited number of variants to save bandwidth
          const maxPreload = Math.min(3, variantsWithImages.length); // Max 3 variants

          console.log(`Preloading ${maxPreload} of ${variantsWithImages.length} variants with secondary images`);

          // Preload the first few variants (usually includes the current one)
          variantsWithImages.slice(0, maxPreload).forEach(([variantId, metafields]) => {
            this.preloadImageSizes(variantId, metafields.secondary_image);
          });
        };

        if (window.requestIdleCallback) {
          window.requestIdleCallback(preloadFunction, { timeout: 2000 });
        } else {
          setTimeout(preloadFunction, 100);
        }
      }

      preloadImageSizes(variantId, secondaryImage) {
        // Only preload the most critical sizes:
        // - 416px: thumbnail (always needed)
        // - 1445px: modal (only if modal exists)
        const sizes = [416]; // Start with just thumbnail

        // Only add modal size if there's actually a modal on this page
        if (this.productModal) {
          sizes.push(1445);
        }

        // Skip the largest size (1946px) for main gallery - it can load on demand
        // This saves significant bandwidth and loading time

        const loadedImages = {};

        // Load images sequentially with priority
        this.preloadImageSequentially(variantId, secondaryImage, sizes, 0, loadedImages);
      }

      preloadImageSequentially(variantId, secondaryImage, sizes, index, loadedImages) {
        if (index >= sizes.length) {
          // All sizes loaded, store the results
          this.preloadedImages.set(variantId, loadedImages);
          console.log(`Completed preloading for variant ${variantId}:`, Object.keys(loadedImages));
          return;
        }

        const size = sizes[index];
        const imageUrl = this.getImageUrl(secondaryImage, size);

        if (!imageUrl) {
          // Skip this size and continue with next
          this.preloadImageSequentially(variantId, secondaryImage, sizes, index + 1, loadedImages);
          return;
        }

        const img = new Image();

        img.onload = () => {
          console.log(`✓ Preloaded ${size}px image for variant ${variantId}`);
          loadedImages[size] = { size, url: imageUrl, img };

          // Continue with next size
          this.preloadImageSequentially(variantId, secondaryImage, sizes, index + 1, loadedImages);
        };

        img.onerror = () => {
          console.warn(`✗ Failed to preload ${size}px image for variant ${variantId}`);

          // Continue with next size even if this one failed
          this.preloadImageSequentially(variantId, secondaryImage, sizes, index + 1, loadedImages);
        };

        // Start loading this image
        img.src = imageUrl;
      }

      connectedCallback() {
        // Listen for variant changes
        if (typeof subscribe !== 'undefined' && typeof PUB_SUB_EVENTS !== 'undefined') {
          this.variantChangeUnsubscriber = subscribe(PUB_SUB_EVENTS.variantChange, (event) => {
            this.handleVariantChange(event.data.variant);
          });
        }

        // Initialize with current variant
        this.initializeWithCurrentVariant();
      }

      disconnectedCallback() {
        if (this.variantChangeUnsubscriber) {
          this.variantChangeUnsubscriber();
        }
      }

      initializeWithCurrentVariant() {
        const variantSelects = this.closest('product-info').querySelector('variant-selects');
        if (variantSelects) {
          const selectedVariantScript = variantSelects.querySelector('[data-selected-variant]');
          if (selectedVariantScript) {
            try {
              const variant = JSON.parse(selectedVariantScript.innerHTML);
              this.handleVariantChange(variant);
            } catch (e) {
              console.error('Error parsing selected variant:', e);
            }
          }
        }
      }

      handleVariantChange(variant) {
        console.log('=== VARIANT CHANGE DETECTED ===');
        console.log('Variant:', variant);
        console.log('Current variant ID:', this.currentVariantId);
        console.log('All variant metafields:', this.variantMetafields);

        if (!variant || variant.id === this.currentVariantId) {
          console.log('Skipping - no variant or same variant');
          return;
        }

        this.currentVariantId = variant.id;

        // Get secondary image from metafields data
        const variantMetafields = this.variantMetafields[variant.id.toString()];
        const secondaryImage = variantMetafields?.secondary_image;

        console.log('Variant metafields for', variant.id, ':', variantMetafields);
        console.log('Secondary image:', secondaryImage);

        if (secondaryImage) {
          const isPreloaded = this.isImagePreloaded(variant.id, 416);
          console.log(`Showing secondary image for variant ${variant.id} (preloaded: ${isPreloaded})`);

          // If not preloaded, preload it now for faster future access
          if (!isPreloaded) {
            this.preloadImageSizes(variant.id, secondaryImage);
          }

          this.showSecondaryImage(variant, secondaryImage);
        } else {
          console.log('No secondary image found, hiding');
          this.hideSecondaryImage();
        }
      }

      showSecondaryImage(variant, secondaryImage) {
        // Update main gallery
        this.updateMainGalleryImage(variant, secondaryImage);

        // Update thumbnail
        this.updateThumbnailImage(variant, secondaryImage);

        // Update modal
        this.updateModalImage(variant, secondaryImage);
      }

      hideSecondaryImage() {
        // Remove secondary image elements if they exist
        if (this.secondaryImageElement) {
          this.secondaryImageElement.remove();
          this.secondaryImageElement = null;
        }

        if (this.secondaryThumbnailElement) {
          this.secondaryThumbnailElement.remove();
          this.secondaryThumbnailElement = null;
        }

        if (this.secondaryModalElement) {
          this.secondaryModalElement.remove();
          this.secondaryModalElement = null;
        }
      }

      updateMainGalleryImage(variant, secondaryImage) {
        if (!this.mediaGallery) {
          console.error('No media gallery found');
          return;
        }

        const galleryList = this.mediaGallery.querySelector('.product__media-list');
        if (!galleryList) {
          console.error('No gallery list found. Available elements:', this.mediaGallery.innerHTML);
          return;
        }

        console.log('Gallery list found:', galleryList);

        // Remove existing secondary image if any
        const existingSecondary = galleryList.querySelector('[data-media-id*="variant-secondary"]');
        if (existingSecondary) {
          console.log('Removing existing secondary image');
          existingSecondary.remove();
        }

        // Create new secondary image element
        const secondaryImageHtml = this.createSecondaryImageHtml(variant, secondaryImage);
        console.log('Created secondary image HTML:', secondaryImageHtml);

        // Insert after the first image (featured image)
        const firstImage = galleryList.querySelector('.product__media-item');
        if (firstImage && firstImage.nextSibling) {
          console.log('Inserting secondary image after first image (with sibling)');
          firstImage.insertAdjacentHTML('afterend', secondaryImageHtml);
        } else if (firstImage) {
          console.log('Inserting secondary image after first image (no sibling)');
          firstImage.insertAdjacentHTML('afterend', secondaryImageHtml);
        } else {
          console.error('No first image found to insert after');
        }

        this.secondaryImageElement = galleryList.querySelector('[data-media-id*="variant-secondary"]');
        console.log('Secondary image element set:', this.secondaryImageElement);
      }

      updateThumbnailImage(variant, secondaryImage) {
        if (!this.mediaGallery) {
          console.error('No media gallery found for thumbnails');
          return;
        }

        const thumbnailList = this.mediaGallery.querySelector('.thumbnail-list');
        if (!thumbnailList) {
          console.error('No thumbnail list found. Available elements:', this.mediaGallery.innerHTML);
          return;
        }

        console.log('Thumbnail list found:', thumbnailList);

        // Remove existing secondary thumbnail if any
        const existingThumbnail = thumbnailList.querySelector('[data-target*="variant-secondary"]');
        if (existingThumbnail) {
          existingThumbnail.remove();
        }

        // Create new secondary thumbnail element
        const secondaryThumbnailHtml = this.createSecondaryThumbnailHtml(variant, secondaryImage);

        // Insert after the first thumbnail
        const firstThumbnail = thumbnailList.querySelector('.thumbnail-list__item');
        if (firstThumbnail) {
          firstThumbnail.insertAdjacentHTML('afterend', secondaryThumbnailHtml);
        }

        this.secondaryThumbnailElement = thumbnailList.querySelector('[data-target*="variant-secondary"]');

        // Add click event listener for the new thumbnail
        if (this.secondaryThumbnailElement) {
          const button = this.secondaryThumbnailElement.querySelector('button');
          if (button) {
            const mediaId = this.secondaryThumbnailElement.dataset.target;
            button.addEventListener('click', () => {
              console.log('Secondary thumbnail clicked, setting active media:', mediaId);
              this.mediaGallery.setActiveMedia(mediaId, false);
            });
          }
        }
      }

      updateModalImage(variant, secondaryImage) {
        if (!this.productModal) return;

        const modalContent = this.productModal.querySelector('.product-media-modal__content');
        if (!modalContent) return;

        // Remove existing secondary modal image if any
        const existingModalImage = modalContent.querySelector('[data-media-id*="variant-secondary"]');
        if (existingModalImage) {
          existingModalImage.remove();
        }

        // Create new secondary modal image element
        const secondaryModalHtml = this.createSecondaryModalHtml(variant, secondaryImage);

        // Insert after the first modal image
        const firstModalImage = modalContent.querySelector('img');
        if (firstModalImage) {
          firstModalImage.insertAdjacentHTML('afterend', secondaryModalHtml);
        }

        this.secondaryModalElement = modalContent.querySelector('[data-media-id*="variant-secondary"]');
      }

      createSecondaryImageHtml(variant, secondaryImage) {
        const sectionId = this.mediaGallery?.dataset?.section || 'MainProduct';
        const mediaId = `${sectionId}-variant-secondary-${variant.id}`;

        console.log('Creating secondary image with media ID:', mediaId);

        return `
          <li
            id="Slide-${sectionId}-variant-secondary-${variant.id}"
            class="product__media-item grid__item slider__slide"
            data-media-id="${mediaId}"
          >
            <div class="product-media-container media-type-image global-media-settings gradient">
              <modal-opener class="product__modal-opener product__modal-opener--image" data-modal="#ProductModal-${sectionId}">
                <span class="product__media-icon motion-reduce quick-add-hidden" aria-hidden="true">
                  <span class="svg-wrapper">
                    <svg viewBox="0 0 20 20" class="icon icon-zoom" aria-hidden="true" focusable="false">
                      <path fill="currentColor" d="M11.5 8.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                      <path fill="currentColor" d="m19 19-3.5-3.5"/>
                    </svg>
                  </span>
                </span>
                <div class="product__media media media--transparent">
                  <img
                    src="${this.getImageUrl(secondaryImage, 1946)}"
                    alt="${secondaryImage.alt || variant.title + ' (secondary)'}"
                    loading="${this.isImagePreloaded(variant.id, 1946) ? 'eager' : 'lazy'}"
                    width="${secondaryImage.width || 1946}"
                    height="${secondaryImage.height || 1946}"
                    style="${this.isImagePreloaded(variant.id, 1946) ? '' : 'opacity: 0; transition: opacity 0.3s ease;'}"
                    onload="this.style.opacity = '1';"
                  >
                </div>
                <button
                  class="product__media-toggle quick-add-hidden"
                  type="button"
                  aria-haspopup="dialog"
                  data-media-id="${mediaId}"
                >
                  <span class="visually-hidden">Open media 2 in modal</span>
                </button>
              </modal-opener>
            </div>
          </li>
        `;
      }

      createSecondaryThumbnailHtml(variant, secondaryImage) {
        const sectionId = this.mediaGallery?.dataset?.section || 'MainProduct';
        const mediaId = `${sectionId}-variant-secondary-${variant.id}`;

        console.log('Creating secondary thumbnail with target:', mediaId);

        return `
          <li
            id="Slide-Thumbnails-${sectionId}-variant-secondary"
            class="thumbnail-list__item slider__slide"
            data-target="${mediaId}"
            data-media-position="2"
          >
            <button
              class="thumbnail global-media-settings global-media-settings--no-shadow"
              aria-label="Load image 2 in gallery view"
              aria-controls="GalleryViewer-${sectionId}"
            >
              <img
                src="${this.getImageUrl(secondaryImage, 416)}"
                alt="${secondaryImage.alt || variant.title + ' (secondary)'}"
                loading="${this.isImagePreloaded(variant.id, 416) ? 'eager' : 'lazy'}"
                width="416"
                height="416"
                style="${this.isImagePreloaded(variant.id, 416) ? '' : 'opacity: 0; transition: opacity 0.3s ease;'}"
                onload="this.style.opacity = '1';"
              >
            </button>
          </li>
        `;
      }

      createSecondaryModalHtml(variant, secondaryImage) {
        return `
          <img
            class="global-media-settings global-media-settings--no-shadow"
            src="${this.getImageUrl(secondaryImage, 1445)}"
            alt="${secondaryImage.alt || variant.title + ' (secondary)'}"
            loading="${this.isImagePreloaded(variant.id, 1445) ? 'eager' : 'lazy'}"
            width="1100"
            height="1100"
            data-media-id="variant-secondary-${variant.id}"
            style="${this.isImagePreloaded(variant.id, 1445) ? '' : 'opacity: 0; transition: opacity 0.3s ease;'}"
            onload="this.style.opacity = '1';"
          >
        `;
      }

      isImagePreloaded(variantId, width) {
        const preloadedVariant = this.preloadedImages.get(variantId.toString());
        return preloadedVariant && preloadedVariant[width];
      }

      getImageUrl(image, width) {
        if (!image) {
          console.warn('No image provided to getImageUrl');
          return '';
        }

        // Now we expect image to be an object with url property from Liquid
        const imageUrl = image.url || image;

        if (!imageUrl) {
          console.warn('Could not extract URL from image object:', image);
          return '';
        }

        return this.addWidthToUrl(imageUrl, width);
      }

      addWidthToUrl(url, width) {
        if (!url) return '';

        // Remove any existing width parameters
        let cleanUrl = url.replace(/[?&]width=\d+/g, '');

        // Add width parameter
        if (cleanUrl.includes('?')) {
          return `${cleanUrl}&width=${width}`;
        } else {
          return `${cleanUrl}?width=${width}`;
        }
      }
    }
  );
}
