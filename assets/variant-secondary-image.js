if (!customElements.get('variant-secondary-image')) {
  customElements.define(
    'variant-secondary-image',
    class VariantSecondaryImage extends HTMLElement {
      constructor() {
        super();
        this.mediaGallery = this.closest('product-info').querySelector('media-gallery');
        this.productModal = this.closest('product-info').querySelector('product-modal');
        this.currentVariantId = null;
        this.secondaryImageElement = null;
        this.secondaryThumbnailElement = null;
        this.secondaryModalElement = null;
        this.variantMetafields = this.getVariantMetafields();
        this.preloadedImages = new Map();

        // Start preloading images
        this.preloadSecondaryImages();
      }

      getVariantMetafields() {
        const variantSelects = this.closest('product-info').querySelector('variant-selects');
        if (variantSelects) {
          const metafieldsScript = variantSelects.querySelector('[data-variant-metafields]');
          if (metafieldsScript) {
            try {
              return JSON.parse(metafieldsScript.innerHTML);
            } catch (e) {
              console.error('Error parsing variant metafields:', e);
            }
          }
        }
        return {};
      }

      preloadSecondaryImages() {
        console.log('Starting to preload secondary images...');

        // Use requestIdleCallback for better performance, fallback to setTimeout
        const preloadFunction = () => {
          const variantsWithImages = Object.entries(this.variantMetafields).filter(
            ([variantId, metafields]) => metafields?.secondary_image
          );

          if (variantsWithImages.length === 0) {
            console.log('No variants with secondary images found');
            return;
          }

          // Strategy: Only preload a limited number of variants to save bandwidth
          const maxPreload = Math.min(3, variantsWithImages.length); // Max 3 variants

          console.log(`Preloading ${maxPreload} of ${variantsWithImages.length} variants with secondary images`);

          // Preload the first few variants (usually includes the current one)
          variantsWithImages.slice(0, maxPreload).forEach(([variantId, metafields]) => {
            this.preloadImageSizes(variantId, metafields.secondary_image);
          });
        };

        if (window.requestIdleCallback) {
          window.requestIdleCallback(preloadFunction, { timeout: 2000 });
        } else {
          setTimeout(preloadFunction, 100);
        }
      }

      preloadImageSizes(variantId, secondaryImage) {
        // Only preload the most critical sizes:
        // - 416px: thumbnail (always needed)
        // - 1445px: modal (only if modal exists)
        const sizes = [416]; // Start with just thumbnail

        // Only add modal size if there's actually a modal on this page
        if (this.productModal) {
          sizes.push(1445);
        }

        // Skip the largest size (1946px) for main gallery - it can load on demand
        // This saves significant bandwidth and loading time

        const loadedImages = {};

        // Load images sequentially with priority
        this.preloadImageSequentially(variantId, secondaryImage, sizes, 0, loadedImages);
      }

      preloadImageSequentially(variantId, secondaryImage, sizes, index, loadedImages) {
        if (index >= sizes.length) {
          // All sizes loaded, store the results
          this.preloadedImages.set(variantId, loadedImages);
          console.log(`Completed preloading for variant ${variantId}:`, Object.keys(loadedImages));
          return;
        }

        const size = sizes[index];
        const imageUrl = this.getImageUrl(secondaryImage, size);

        if (!imageUrl) {
          // Skip this size and continue with next
          this.preloadImageSequentially(variantId, secondaryImage, sizes, index + 1, loadedImages);
          return;
        }

        const img = new Image();

        img.onload = () => {
          console.log(`✓ Preloaded ${size}px image for variant ${variantId}`);
          loadedImages[size] = { size, url: imageUrl, img };

          // Continue with next size
          this.preloadImageSequentially(variantId, secondaryImage, sizes, index + 1, loadedImages);
        };

        img.onerror = () => {
          console.warn(`✗ Failed to preload ${size}px image for variant ${variantId}`);

          // Continue with next size even if this one failed
          this.preloadImageSequentially(variantId, secondaryImage, sizes, index + 1, loadedImages);
        };

        // Start loading this image
        img.src = imageUrl;
      }

      connectedCallback() {
        // Listen for variant changes
        if (typeof subscribe !== 'undefined' && typeof PUB_SUB_EVENTS !== 'undefined') {
          this.variantChangeUnsubscriber = subscribe(PUB_SUB_EVENTS.variantChange, (event) => {
            this.handleVariantChange(event.data.variant);
          });
        }

        // Initialize with current variant
        this.initializeWithCurrentVariant();
      }

      disconnectedCallback() {
        if (this.variantChangeUnsubscriber) {
          this.variantChangeUnsubscriber();
        }
      }

      initializeWithCurrentVariant() {
        const variantSelects = this.closest('product-info').querySelector('variant-selects');
        if (variantSelects) {
          const selectedVariantScript = variantSelects.querySelector('[data-selected-variant]');
          if (selectedVariantScript) {
            try {
              const variant = JSON.parse(selectedVariantScript.innerHTML);
              this.handleVariantChange(variant);
            } catch (e) {
              console.error('Error parsing selected variant:', e);
            }
          }
        }
      }

      handleVariantChange(variant) {
        if (!variant || variant.id === this.currentVariantId) return;

        this.currentVariantId = variant.id;

        // Get secondary image from metafields data
        const variantMetafields = this.variantMetafields[variant.id.toString()];
        const secondaryImage = variantMetafields?.secondary_image;

        if (secondaryImage) {
          const isPreloaded = this.isImagePreloaded(variant.id, 416);
          console.log(`Showing secondary image for variant ${variant.id} (preloaded: ${isPreloaded})`);

          // If not preloaded, preload it now for faster future access
          if (!isPreloaded) {
            this.preloadImageSizes(variant.id, secondaryImage);
          }

          this.showSecondaryImage(variant, secondaryImage);
        } else {
          this.hideSecondaryImage();
        }
      }

      showSecondaryImage(variant, secondaryImage) {
        // Update main gallery
        this.updateMainGalleryImage(variant, secondaryImage);

        // Update thumbnail
        this.updateThumbnailImage(variant, secondaryImage);

        // Update modal
        this.updateModalImage(variant, secondaryImage);
      }

      hideSecondaryImage() {
        // Remove secondary image elements if they exist
        if (this.secondaryImageElement) {
          this.secondaryImageElement.remove();
          this.secondaryImageElement = null;
        }

        if (this.secondaryThumbnailElement) {
          this.secondaryThumbnailElement.remove();
          this.secondaryThumbnailElement = null;
        }

        if (this.secondaryModalElement) {
          this.secondaryModalElement.remove();
          this.secondaryModalElement = null;
        }
      }

      updateMainGalleryImage(variant, secondaryImage) {
        if (!this.mediaGallery) return;

        const galleryList = this.mediaGallery.querySelector('.product__media-list');
        if (!galleryList) return;

        // Remove existing secondary image if any
        const existingSecondary = galleryList.querySelector('[data-media-id*="variant-secondary"]');
        if (existingSecondary) {
          existingSecondary.remove();
        }

        // Create new secondary image element
        const secondaryImageHtml = this.createSecondaryImageHtml(variant, secondaryImage);

        // Insert after the first image (featured image)
        const firstImage = galleryList.querySelector('.product__media-item');
        if (firstImage && firstImage.nextSibling) {
          firstImage.insertAdjacentHTML('afterend', secondaryImageHtml);
        } else if (firstImage) {
          firstImage.insertAdjacentHTML('afterend', secondaryImageHtml);
        }

        this.secondaryImageElement = galleryList.querySelector('[data-media-id*="variant-secondary"]');
      }

      updateThumbnailImage(variant, secondaryImage) {
        if (!this.mediaGallery) return;

        const thumbnailList = this.mediaGallery.querySelector('.thumbnail-list');
        if (!thumbnailList) return;

        // Remove existing secondary thumbnail if any
        const existingThumbnail = thumbnailList.querySelector('[data-target*="variant-secondary"]');
        if (existingThumbnail) {
          existingThumbnail.remove();
        }

        // Create new secondary thumbnail element
        const secondaryThumbnailHtml = this.createSecondaryThumbnailHtml(variant, secondaryImage);

        // Insert after the first thumbnail
        const firstThumbnail = thumbnailList.querySelector('.thumbnail-list__item');
        if (firstThumbnail) {
          firstThumbnail.insertAdjacentHTML('afterend', secondaryThumbnailHtml);
        }

        this.secondaryThumbnailElement = thumbnailList.querySelector('[data-target*="variant-secondary"]');

        // Add click event listener
        if (this.secondaryThumbnailElement) {
          const button = this.secondaryThumbnailElement.querySelector('button');
          if (button) {
            button.addEventListener('click', () => {
              this.mediaGallery.setActiveMedia(`${this.mediaGallery.dataset.section || 'MainProduct'}-variant-secondary-${variant.id}`, false);
            });
          }
        }
      }

      updateModalImage(variant, secondaryImage) {
        if (!this.productModal) return;

        const modalContent = this.productModal.querySelector('.product-media-modal__content');
        if (!modalContent) return;

        // Remove existing secondary modal image if any
        const existingModalImage = modalContent.querySelector('[data-media-id*="variant-secondary"]');
        if (existingModalImage) {
          existingModalImage.remove();
        }

        // Create new secondary modal image element
        const secondaryModalHtml = this.createSecondaryModalHtml(variant, secondaryImage);

        // Insert after the first modal image
        const firstModalImage = modalContent.querySelector('img');
        if (firstModalImage) {
          firstModalImage.insertAdjacentHTML('afterend', secondaryModalHtml);
        }

        this.secondaryModalElement = modalContent.querySelector('[data-media-id*="variant-secondary"]');
      }

      createSecondaryImageHtml(variant, secondaryImage) {
        const sectionId = this.mediaGallery?.dataset?.section || 'MainProduct';

        return `
          <li
            id="Slide-${sectionId}-variant-secondary-${variant.id}"
            class="product__media-item grid__item slider__slide"
            data-media-id="${sectionId}-variant-secondary-${variant.id}"
          >
            <div class="product-media-container media-type-image global-media-settings gradient">
              <modal-opener class="product__modal-opener product__modal-opener--image" data-modal="#ProductModal-${sectionId}">
                <span class="product__media-icon motion-reduce quick-add-hidden" aria-hidden="true">
                  <span class="svg-wrapper">
                    <svg viewBox="0 0 20 20" class="icon icon-zoom" aria-hidden="true" focusable="false">
                      <path fill="currentColor" d="M11.5 8.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                      <path fill="currentColor" d="m19 19-3.5-3.5"/>
                    </svg>
                  </span>
                </span>
                <div class="product__media media media--transparent">
                  <img
                    src="${this.getImageUrl(secondaryImage, 1946)}"
                    alt="${secondaryImage.alt || variant.title + ' (secondary)'}"
                    loading="${this.isImagePreloaded(variant.id, 1946) ? 'eager' : 'lazy'}"
                    width="${secondaryImage.width || 1946}"
                    height="${secondaryImage.height || 1946}"
                    style="${this.isImagePreloaded(variant.id, 1946) ? '' : 'opacity: 0; transition: opacity 0.3s ease;'}"
                    onload="this.style.opacity = '1';"
                  >
                </div>
                <button
                  class="product__media-toggle quick-add-hidden"
                  type="button"
                  aria-haspopup="dialog"
                  data-media-id="variant-secondary-${variant.id}"
                >
                  <span class="visually-hidden">Open media 2 in modal</span>
                </button>
              </modal-opener>
            </div>
          </li>
        `;
      }

      createSecondaryThumbnailHtml(variant, secondaryImage) {
        const sectionId = this.mediaGallery?.dataset?.section || 'MainProduct';

        return `
          <li
            id="Slide-Thumbnails-${sectionId}-variant-secondary"
            class="thumbnail-list__item slider__slide"
            data-target="${sectionId}-variant-secondary-${variant.id}"
            data-media-position="2"
          >
            <button
              class="thumbnail global-media-settings global-media-settings--no-shadow"
              aria-label="Load image 2 in gallery view"
              aria-controls="GalleryViewer-${sectionId}"
            >
              <img
                src="${this.getImageUrl(secondaryImage, 416)}"
                alt="${secondaryImage.alt || variant.title + ' (secondary)'}"
                loading="${this.isImagePreloaded(variant.id, 416) ? 'eager' : 'lazy'}"
                width="416"
                height="416"
                style="${this.isImagePreloaded(variant.id, 416) ? '' : 'opacity: 0; transition: opacity 0.3s ease;'}"
                onload="this.style.opacity = '1';"
              >
            </button>
          </li>
        `;
      }

      createSecondaryModalHtml(variant, secondaryImage) {
        return `
          <img
            class="global-media-settings global-media-settings--no-shadow"
            src="${this.getImageUrl(secondaryImage, 1445)}"
            alt="${secondaryImage.alt || variant.title + ' (secondary)'}"
            loading="${this.isImagePreloaded(variant.id, 1445) ? 'eager' : 'lazy'}"
            width="1100"
            height="1100"
            data-media-id="variant-secondary-${variant.id}"
            style="${this.isImagePreloaded(variant.id, 1445) ? '' : 'opacity: 0; transition: opacity 0.3s ease;'}"
            onload="this.style.opacity = '1';"
          >
        `;
      }

      isImagePreloaded(variantId, width) {
        const preloadedVariant = this.preloadedImages.get(variantId.toString());
        return preloadedVariant && preloadedVariant[width];
      }

      getImageUrl(image, width) {
        if (!image) {
          console.warn('No image provided to getImageUrl');
          return '';
        }

        // Now we expect image to be an object with url property from Liquid
        const imageUrl = image.url || image;

        if (!imageUrl) {
          console.warn('Could not extract URL from image object:', image);
          return '';
        }

        return this.addWidthToUrl(imageUrl, width);
      }

      addWidthToUrl(url, width) {
        if (!url) return '';

        // Remove any existing width parameters
        let cleanUrl = url.replace(/[?&]width=\d+/g, '');

        // Add width parameter
        if (cleanUrl.includes('?')) {
          return `${cleanUrl}&width=${width}`;
        } else {
          return `${cleanUrl}?width=${width}`;
        }
      }
    }
  );
}
