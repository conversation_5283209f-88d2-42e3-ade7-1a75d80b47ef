{% comment %}
  Renders a product media modal. Also see 'product-media-gallery'

  Accepts:
  - product: {Object} Product liquid object
  - variant_images: {Array} Product images associated with a variant

  Usage:
  {% render 'product-media-modal' %}
{% endcomment %}

<product-modal id="ProductModal-{{ section.id }}" class="product-media-modal media-modal">
  <div
    class="product-media-modal__dialog color-{{ section.settings.color_scheme }} gradient"
    role="dialog"
    aria-label="{{ 'products.modal.label' | t }}"
    aria-modal="true"
    tabindex="-1"
  >
    <button
      id="ModalClose-{{ section.id }}"
      type="button"
      class="product-media-modal__toggle"
      aria-label="{{ 'accessibility.close' | t }}"
    >
      {{ 'icon-close.svg' | inline_asset_content }}
    </button>

    <div
      class="product-media-modal__content color-{{ section.settings.color_scheme }} gradient"
      role="document"
      aria-label="{{ 'products.modal.label' | t }}"
      tabindex="0"
    >
      {%- liquid
        if product.selected_or_first_available_variant.featured_media != null
          assign media = product.selected_or_first_available_variant.featured_media
          render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: section.settings.hide_variants
        endif
      -%}

      {%- comment -%} Add variant secondary image to modal if available {%- endcomment -%}
      {%- if product.selected_or_first_available_variant.metafields.custom.secondary_image -%}
        {%- assign variant_secondary_image = product.selected_or_first_available_variant.metafields.custom.secondary_image -%}
        <img
          class="global-media-settings global-media-settings--no-shadow"
          srcset="
            {%- if variant_secondary_image.width >= 550 -%}{{ variant_secondary_image | image_url: width: 550 }} 550w,{%- endif -%}
            {%- if variant_secondary_image.width >= 1100 -%}{{ variant_secondary_image | image_url: width: 1100 }} 1100w,{%- endif -%}
            {%- if variant_secondary_image.width >= 1445 -%}{{ variant_secondary_image | image_url: width: 1445 }} 1445w,{%- endif -%}
            {%- if variant_secondary_image.width >= 1680 -%}{{ variant_secondary_image | image_url: width: 1680 }} 1680w,{%- endif -%}
            {%- if variant_secondary_image.width >= 2048 -%}{{ variant_secondary_image | image_url: width: 2048 }} 2048w,{%- endif -%}
            {%- if variant_secondary_image.width >= 2200 -%}{{ variant_secondary_image | image_url: width: 2200 }} 2200w,{%- endif -%}
            {%- if variant_secondary_image.width >= 2890 -%}{{ variant_secondary_image | image_url: width: 2890 }} 2890w,{%- endif -%}
            {%- if variant_secondary_image.width >= 4096 -%}{{ variant_secondary_image | image_url: width: 4096 }} 4096w,{%- endif -%}
            {{ variant_secondary_image | image_url }} {{ variant_secondary_image.width }}w
          "
          sizes="(min-width: 750px) calc(100vw - 22rem), 1100px"
          src="{{ variant_secondary_image | image_url: width: 1445 }}"
          alt="{{ variant_secondary_image.alt | default: product.selected_or_first_available_variant.title | append: ' (secondary)' | escape }}"
          loading="lazy"
          width="1100"
          height="{{ 1100 | divided_by: variant_secondary_image.aspect_ratio | ceil }}"
          data-media-id="variant-secondary-{{ product.selected_or_first_available_variant.id }}"
        >
      {%- endif -%}

      {%- for media in product.media -%}
        {%- liquid
          if section.settings.hide_variants and variant_images contains media.src or variant_images contains media.id
            assign variant_image = true
          else
            assign variant_image = false
          endif

          unless media.id == product.selected_or_first_available_variant.featured_media.id
            render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: variant_image
          endunless
        -%}
      {%- endfor -%}
    </div>
  </div>
</product-modal>
